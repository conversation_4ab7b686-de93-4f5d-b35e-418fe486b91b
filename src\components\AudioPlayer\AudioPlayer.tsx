'use client';

import React from 'react';
import { AudioPlayerProps } from './types';
import styles from './AudioPlayer.module.css';

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioFile,
  title,
  className = '',
  variant = 'default',
  autoPlay = false,
  showTitle = true,
}) => {
  const audioUrl = audioFile?.data?.attributes?.url;

  if (!audioUrl) {
    return null;
  }

  return (
    <div className={`${styles.audioPlayer} ${styles[variant]} ${className}`}>
      {showTitle && title && <div className={styles.audioTitle}>{title}</div>}

      <div className={styles.audio_section}>
        <audio
          className={styles.audio_player}
          src={audioUrl}
          controls
          controlsList="nodownload"
          // preload="metadata"
          // autoPlay={autoPlay}
        />
      </div>
    </div>
  );
};

export default AudioPlayer;
