'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AudioPlayerProps, AudioPlayerState } from './types';
import styles from './AudioPlayer.module.css';

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioFile,
  title,
  className = '',
  variant = 'default',
  autoPlay = false,
  showTitle = true,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isLoading: false,
  });

  const audioUrl = audioFile?.data?.attributes?.url;

  // Debug logging
  console.log('AudioPlayer - audioFile:', audioFile);
  console.log('AudioPlayer - audioUrl:', audioUrl);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) return;

    // Set loading state immediately
    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    // Safety timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('Loading timeout - forcing loading state to false');
      setState(prev => ({ ...prev, isLoading: false }));
    }, 10000); // 10 seconds max loading time

    const updateDuration = () => {
      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        console.log('Duration updated:', audio.duration);
        clearTimeout(loadingTimeout); // Clear the safety timeout
        setState(prev => ({
          ...prev,
          duration: audio.duration,
          isLoading: false,
        }));
        return true; // Indicate success
      }
      return false; // Indicate failure
    };

    let durationFound = false;

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      if (updateDuration()) {
        durationFound = true;
      }
    };

    const handleLoadedData = () => {
      console.log('Audio data loaded, duration:', audio.duration);
      if (updateDuration()) {
        durationFound = true;
      }
    };

    const handleCanPlay = () => {
      console.log('Audio can play, duration:', audio.duration);
      if (updateDuration()) {
        durationFound = true;
      }
    };

    const handleDurationChange = () => {
      console.log('Duration changed:', audio.duration);
      if (updateDuration()) {
        durationFound = true;
      }
    };

    const handleTimeUpdate = () => {
      setState(prev => {
        const newState = {
          ...prev,
          currentTime: audio.currentTime,
        };

        // Sometimes duration is only available after timeupdate
        if (
          !prev.duration &&
          audio.duration &&
          isFinite(audio.duration) &&
          audio.duration > 0
        ) {
          updateDuration();
        }

        return newState;
      });
    };

    const handleEnded = () => {
      setState(prev => ({
        ...prev,
        isPlaying: false,
        currentTime: 0,
      }));
    };

    const handleLoadStart = () => {
      if (!durationFound) {
        setState(prev => ({
          ...prev,
          isLoading: true,
        }));
      }
    };

    const handleError = (e: Event) => {
      console.error('Audio loading error:', e);

      // Try without CORS if it fails
      if (audio.crossOrigin) {
        console.log('Retrying without CORS...');
        audio.crossOrigin = null;
        audio.load();
        return;
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load audio',
      }));
    };

    // Add all possible event listeners to capture duration
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('canplaythrough', handleCanPlay);
    audio.addEventListener('durationchange', handleDurationChange);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('error', handleError);

    // Try to load metadata immediately
    if (audio.readyState >= 1) {
      // Metadata already loaded
      if (updateDuration()) {
        durationFound = true;
      }
    } else {
      // Force load metadata
      audio.load();
    }

    // Fallback: Check duration periodically for the first few seconds
    let checkCount = 0;
    const maxChecks = 50; // 5 seconds at 100ms intervals

    const durationCheckInterval = setInterval(() => {
      checkCount++;

      if (durationFound || checkCount >= maxChecks) {
        clearInterval(durationCheckInterval);
        if (!durationFound && checkCount >= maxChecks) {
          console.log('Duration check timeout - stopping loading state');
          setState(prev => ({ ...prev, isLoading: false }));
        }
        return;
      }

      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        if (updateDuration()) {
          durationFound = true;
          clearInterval(durationCheckInterval);
        }
      }
    }, 100);

    return () => {
      clearInterval(durationCheckInterval);
      clearTimeout(loadingTimeout);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('canplaythrough', handleCanPlay);
      audio.removeEventListener('durationchange', handleDurationChange);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('error', handleError);
    };
  }, [audioUrl]);

  const togglePlayPause = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (state.isPlaying) {
      audio.pause();
      setState(prev => ({
        ...prev,
        isPlaying: false,
      }));
    } else {
      try {
        await audio.play();
        setState(prev => ({
          ...prev,
          isPlaying: true,
        }));

        // Sometimes duration is only available after play starts
        if (
          audio.duration &&
          isFinite(audio.duration) &&
          audio.duration > 0 &&
          !state.duration
        ) {
          console.log('Duration available after play:', audio.duration);
          setState(prev => ({
            ...prev,
            duration: audio.duration,
            isLoading: false,
          }));
        }
      } catch (error) {
        console.error('Play failed:', error);
        setState(prev => ({
          ...prev,
          isPlaying: false,
          error: 'Playback failed',
        }));
      }
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const progressBar = progressRef.current;
    if (!audio || !progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const newTime = (clickX / width) * state.duration;

    audio.currentTime = newTime;
    setState(prev => ({
      ...prev,
      currentTime: newTime,
    }));
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    const newVolume = parseFloat(e.target.value);

    if (audio) {
      audio.volume = newVolume;
    }

    setState(prev => ({
      ...prev,
      volume: newVolume,
    }));
  };

  const formatTime = (time: number): string => {
    if (isNaN(time) || time === 0) return '0:00';
    if (!isFinite(time)) return '0:00';

    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage =
    state.duration > 0 ? (state.currentTime / state.duration) * 100 : 0;

  if (!audioUrl) {
    return null;
  }

  return (
    <div className={`${styles.audioPlayer} ${styles[variant]} ${className}`}>
      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
        autoPlay={autoPlay}
        controls={false}
        style={{ display: 'none' }}
      />

      {showTitle && title && <div className={styles.audioTitle}>{title}</div>}

      <div className={styles.controls}>
        <button
          className={styles.playButton}
          onClick={togglePlayPause}
          disabled={state.isLoading}
          aria-label={state.isPlaying ? 'Pause' : 'Play'}
        >
          {state.isLoading ? (
            <div className={styles.spinner} />
          ) : state.isPlaying ? (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="6" y="4" width="4" height="16" fill="currentColor" />
              <rect x="14" y="4" width="4" height="16" fill="currentColor" />
            </svg>
          ) : (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <polygon points="8,5 19,12 8,19" fill="currentColor" />
            </svg>
          )}
        </button>

        <div className={styles.progressContainer}>
          <div
            ref={progressRef}
            className={styles.progressBar}
            onClick={handleProgressClick}
          >
            <div
              className={styles.progressFill}
              style={{ width: `${progressPercentage}%` }}
            />
            <div
              className={styles.progressHandle}
              style={{ left: `${progressPercentage}%` }}
            />
          </div>

          <div className={styles.timeDisplay}>
            <span className={styles.currentTime}>
              {formatTime(state.currentTime)}
            </span>
            <span className={styles.separator}>/</span>
            <span className={styles.duration}>
              {state.isLoading ? '--:--' : formatTime(state.duration)}
            </span>
          </div>
        </div>

        <div className={styles.volumeContainer}>
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            className={styles.volumeIcon}
          >
            <polygon
              points="11,5 6,9 2,9 2,15 6,15 11,19"
              fill="currentColor"
            />
            <path
              d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={state.volume}
            onChange={handleVolumeChange}
            className={styles.volumeSlider}
            aria-label="Volume"
          />
        </div>
      </div>

      {state.error && <div className={styles.error}>{state.error}</div>}
    </div>
  );
};

export default AudioPlayer;
