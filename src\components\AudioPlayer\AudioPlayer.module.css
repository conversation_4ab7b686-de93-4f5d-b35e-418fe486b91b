.audioPlayer {
  width: 100%;
  margin: 24px 0;
}

.audioTitle {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.audio_section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.audio_player {
  width: 100%;
  height: 50px;
}

/* Variants */
.compact {
  margin: 16px 0;
}

.inline {
  margin: 8px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .audioPlayer {
    margin: 16px 0;
  }

  .audioTitle {
    font-size: 12px;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .audioPlayer {
    margin: 12px 0;
  }

  .audioTitle {
    font-size: 11px;
    margin-bottom: 6px;
  }
}
