/* Base styles - Mobile First Approach */
.audioPlayer {
  width: 100%;
  margin: 12px 0;
  padding: 0 8px;
  box-sizing: border-box;
}

.audioTitle {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
  line-height: 1.4;
  word-wrap: break-word;
}

.audio_section {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.audio_player {
  width: 100%;
  height: 40px;
  min-height: 40px;
  border-radius: 4px;
  outline: none;
}

/* Ensure audio controls are always visible and accessible */
.audio_player::-webkit-media-controls-panel {
  background-color: #f8f9fa;
  border-radius: 4px;
}

.audio_player::-webkit-media-controls-play-button,
.audio_player::-webkit-media-controls-pause-button {
  background-color: #007bff;
  border-radius: 50%;
}

/* Variants - Mobile First */
.compact {
  margin: 8px 0;
  padding: 0 4px;
}

.compact .audio_player {
  height: 36px;
  min-height: 36px;
}

.inline {
  margin: 4px 0;
  padding: 0;
}

.inline .audio_player {
  height: 32px;
  min-height: 32px;
}

.inline .audioTitle {
  font-size: 11px;
  margin-bottom: 4px;
}

/* Small Mobile Devices (320px - 480px) */
@media (min-width: 320px) and (max-width: 480px) {
  .audioPlayer {
    margin: 10px 0;
    padding: 0 6px;
  }

  .audioTitle {
    font-size: 11px;
    margin-bottom: 6px;
  }

  .audio_section {
    gap: 6px;
  }

  .audio_player {
    height: 38px;
    min-height: 38px;
  }
}

/* Large Mobile / Small Tablet (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .audioPlayer {
    margin: 16px 0;
    padding: 0 12px;
  }

  .audioTitle {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .audio_section {
    gap: 10px;
  }

  .audio_player {
    height: 45px;
    min-height: 45px;
  }

  .compact .audio_player {
    height: 40px;
    min-height: 40px;
  }

  .inline .audio_player {
    height: 35px;
    min-height: 35px;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .audioPlayer {
    margin: 20px 0;
    padding: 0 16px;
  }

  .audioTitle {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .audio_section {
    gap: 12px;
  }

  .audio_player {
    height: 50px;
    min-height: 50px;
  }

  .compact .audio_player {
    height: 45px;
    min-height: 45px;
  }

  .inline .audio_player {
    height: 38px;
    min-height: 38px;
  }
}

/* Desktop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .audioPlayer {
    margin: 24px 0;
    padding: 0 20px;
  }

  .audioTitle {
    font-size: 15px;
    margin-bottom: 14px;
  }

  .audio_section {
    gap: 14px;
  }

  .audio_player {
    height: 52px;
    min-height: 52px;
  }

  .compact {
    margin: 18px 0;
    padding: 0 16px;
  }

  .compact .audio_player {
    height: 48px;
    min-height: 48px;
  }

  .inline {
    margin: 10px 0;
  }

  .inline .audio_player {
    height: 40px;
    min-height: 40px;
  }
}

/* Large Desktop (1441px+) */
@media (min-width: 1441px) {
  .audioPlayer {
    margin: 28px 0;
    padding: 0 24px;
  }

  .audioTitle {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .audio_section {
    gap: 16px;
  }

  .audio_player {
    height: 55px;
    min-height: 55px;
  }

  .compact {
    margin: 22px 0;
    padding: 0 20px;
  }

  .compact .audio_player {
    height: 50px;
    min-height: 50px;
  }

  .inline {
    margin: 12px 0;
  }

  .inline .audio_player {
    height: 42px;
    min-height: 42px;
  }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .audio_player {
    border: 0.5px solid #e0e0e0;
  }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .audioPlayer {
    margin: 8px 0;
  }

  .audioTitle {
    margin-bottom: 6px;
  }

  .audio_player {
    height: 36px;
    min-height: 36px;
  }
}

/* Accessibility and Focus States */
.audio_player:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .audioTitle {
    color: #f8f9fa;
  }

  .audio_player::-webkit-media-controls-panel {
    background-color: #343a40;
  }
}
