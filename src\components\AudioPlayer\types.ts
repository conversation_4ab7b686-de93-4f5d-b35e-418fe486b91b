export interface AudioFileData {
  id: number;
  attributes: {
    alternativeText?: string;
    caption?: string;
    createdAt: string;
    ext: string;
    formats?: null;
    hash: string;
    height?: number;
    mime: string;
    name: string;
    previewUrl?: null;
    provider: string;
    provider_metadata?: null;
    size: number;
    updatedAt: string;
    url: string;
    width?: number;
  };
}

export interface AudioPlayerProps {
  audioFile?: {
    data?: AudioFileData;
  };
  title?: string;
  className?: string;
  variant?: 'default' | 'compact' | 'inline';
  autoPlay?: boolean;
  showTitle?: boolean;
}

export interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLoading: boolean;
  error?: string;
}

export interface BlogAudioData {
  audio_file?: {
    data?: AudioFileData;
  };
}
