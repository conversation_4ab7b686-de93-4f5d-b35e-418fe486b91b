# AudioPlayer Component

A responsive audio player component for blog posts that integrates with Strapi CMS audio files.

## Features

- **Play/Pause Controls**: Clean play/pause button with loading states
- **Progress Bar**: Interactive progress bar with time display
- **Volume Control**: Volume slider (hidden on mobile for space)
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Multiple Variants**: Default, compact, and inline variants
- **Strapi Integration**: Seamlessly works with Strapi audio_file field

## Usage

### Basic Usage

```tsx
import AudioPlayer from '@components/AudioPlayer';

<AudioPlayer
  audioFile={blogData.audio_file}
  title="Listen to this blog post"
  variant="default"
  showTitle={true}
/>;
```

### Props

| Prop        | Type                                 | Default     | Description                   |
| ----------- | ------------------------------------ | ----------- | ----------------------------- |
| `audioFile` | `{data?: AudioFileData}`             | -           | Audio file data from Strapi   |
| `title`     | `string`                             | -           | Title to display above player |
| `className` | `string`                             | `''`        | Additional CSS classes        |
| `variant`   | `'default' \| 'compact' \| 'inline'` | `'default'` | Player size variant           |
| `autoPlay`  | `boolean`                            | `false`     | Auto-play audio on load       |
| `showTitle` | `boolean`                            | `true`      | Show/hide title above player  |

### Variants

- **default**: Full-featured player with all controls
- **compact**: Smaller player without volume control
- **inline**: Minimal player for blog cards

## Integration Points

### Blog Detail Pages

- Added to `BlogHeroSection` component
- Displays below blog description
- Uses `default` variant on desktop, `compact` on mobile

### Blog Listing Pages

- Added to `BlogListing` component
- Displays in blog cards
- Uses `inline` variant
- Click events are prevented from triggering navigation

## Strapi Configuration

Ensure your Strapi blog content type includes an `audio_file` field:

```javascript
// In your Strapi blog content type
audio_file: {
  type: 'media',
  multiple: false,
  required: false,
  allowedTypes: ['audio']
}
```

## Styling

The component uses CSS Modules with responsive breakpoints:

- **Desktop**: Full controls with volume slider
- **Tablet**: Compact controls, hidden volume
- **Mobile**: Touch-optimized controls, larger touch targets

## Browser Support

- Modern browsers with HTML5 audio support
- Progressive enhancement for older browsers
- Graceful fallback when audio files are unavailable

## Recent Fixes (Based on Figma Design & User Feedback)

### ✅ POSITIONING FIXED

- **Desktop Layout**: Audio player now positioned between cover image and content sections
- **Mobile Layout**: Audio player positioned between image and table of contents
- **Proper Spacing**: Added dedicated wrapper classes with responsive padding
- **Layout Compliance**: Matches exact Figma design positioning

### ✅ DURATION LOADING ISSUES FIXED

- **Multiple Event Listeners**: Added `loadedmetadata`, `loadeddata`, `canplay`, `canplaythrough`, `durationchange`
- **Periodic Duration Check**: Added interval-based fallback to check duration every 100ms for 5 seconds
- **Play-Time Duration Detection**: Duration captured when play button is pressed (fallback method)
- **Enhanced Error Handling**: CORS fallback with automatic retry
- **Preload Strategy**: Changed from `metadata` to `auto` for better loading
- **Ready State Check**: Immediate duration check if metadata already loaded

### Audio Loading Reliability Improvements

- **Async Play Handling**: Proper promise-based play/pause with error catching
- **State Management**: Better loading states and error recovery
- **Debug Logging**: Comprehensive console logging for troubleshooting
- **Cross-Origin Handling**: Automatic CORS retry on failure

## Testing

To test the audio player:

1. **Development Server**: Run `npm run dev` (currently running on http://localhost:3000)
2. **Blog Pages**: Navigate to any blog post with audio_file data
3. **Check Console**: Look for audio loading debug messages
4. **Duration Display**: Should show actual duration instead of 0:00
5. **Responsive**: Test on different screen sizes
6. **Audio Controls**: Verify play/pause, progress, and volume work
7. **Blog Listing**: Check audio players in blog cards don't interfere with navigation

### Debugging Audio Issues

- Open browser console to see audio loading logs
- Check if audio files are accessible (CORS issues)
- Verify Strapi audio_file field is populated
- Test with different audio formats (MP3, WAV, etc.)

## Accessibility

- ARIA labels for all interactive elements
- Keyboard navigation support
- Screen reader compatible
- High contrast mode support
